import json, strutils
import utils/log

proc decodeHtmlEntities(text: string): string =
  if text.len == 0: return text
  text.multiReplace([
    ("&amp;", "&"),
    ("&lt;", "<"), 
    ("&gt;", ">"),
    ("&quot;", "\""),
    ("&#39;", "'")
  ])

proc formatReceived*(event: JsonNode) =
  let postType = event{"post_type"}.getStr()
  
  case postType:
  of "meta_event":
    let metaType = event{"meta_event_type"}.getStr()
    if metaType == "heartbeat":
      logDebug("💓 心跳包")
    elif metaType == "lifecycle" and event{"sub_type"}.getStr() == "connect":
      logInfo("🤖 机器人连接: " & $event{"self_id"}.getInt())
      
  of "message":
    # 解码HTML实体
    if event.hasKey("raw_message"):
      let decoded = decodeHtmlEntities(event{"raw_message"}.getStr())
      event["raw_message"] = %decoded
    
    let sender = event{"sender"}{"nickname"}.getStr("未知用户")
    let msgType = event{"message_type"}.getStr()
    let rawMsg = event{"raw_message"}.getStr()
    
    case msgType:
    of "private":
      logInfo("📩 私聊消息 [" & sender & "(" & $event{"user_id"}.getInt() & ")]: " & rawMsg)
    of "group":
      logInfo("💬 群消息 [" & $event{"group_id"}.getInt() & "] " & sender & ": " & rawMsg)
      
  of "notice":
    logInfo("📢 通知事件: " & event{"notice_type"}.getStr("未知"))
  of "request":
    logInfo("🔔 请求事件: " & event{"request_type"}.getStr("未知"))

proc formatSent*(action: string, params: JsonNode) =
  case action:
  of "send_private_msg":
    logInfo("📤 发送私聊消息 -> " & $params{"user_id"}.getInt() & ": " & params{"message"}.getStr())
  of "send_group_msg":
    logInfo("📤 发送群消息 -> " & $params{"group_id"}.getInt() & ": " & params{"message"}.getStr())
  else:
    logDebug("📤 发送API请求: " & action)