
import json, ws, asyncdispatch
import utils/log

type
  Bot* = ref object
    conn*: WebSocket

proc newBot*(conn: WebSocket): Bot =
  Bot(conn: conn)

proc send*(bot: Bo<PERSON>, data: JsonNode) {.async.} =
  try:
    let payload = %*{
      "action": data["action"],
      "params": data["params"]
    }
    await bot.conn.send($payload)
    logInfo("发送数据: " & $payload)
  except Exception as e:
    logError("发送失败: " & e.msg)

proc sendGroupMsg*(bot: Bot, groupId: int64, message: string) {.async.} =
  let data = %*{
    "action": "send_group_msg",
    "params": {
      "group_id": groupId,
      "message": message
    }
  }
  await bot.send(data)

proc sendPrivateMsg*(bot: Bot, userId: int64, message: string) {.async.} =
  let data = %*{
    "action": "send_private_msg", 
    "params": {
      "user_id": userId,
      "message": message
    }
  }
  await bot.send(data)

# 绑定所有API方法到Bot 将api.nim中的所有API代码插入到此处开始
include api

