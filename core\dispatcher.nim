import json, algorithm, strutils, asyncdispatch
import utils/log, bot, ws
from matcher import globalMatchers, Matcher

type
  Dispatcher* = ref object

proc handle*(dispatcher: Dispatcher, event: JsonNode,
    conn: WebSocket) {.async, gcsafe.} =
  if event{"post_type"}.getStr() != "message" or event{"message_type"}.getStr() != "group":
    return

  let botInstance = newBot(conn)

  # 使用sorted而不是sort，更函数式
  for m in globalMatchers.sortedByIt(it.priority):
    if m.eventType.len == 0 or m.eventType == event{"post_type"}.getStr():
      if m.rule.isNil or m.rule(event):
        try:
          await m.handler(botInstance, event)
        except CatchableError as e: # 更具体的异常类型
          logError("处理器执行失败: ", e.msg)

        if m.`block`:
          break

proc newDispatcher*(): Dispatcher = Dispatcher()






